/**
 * دالة إصلاح الأرباح في قاعدة البيانات
 * تعيد حساب جميع الأرباح مع طرح مصاريف النقل
 */

/**
 * إصلاح إجمالي الأرباح في الخزينة من المعاملات الفعلية
 * @returns {Promise<Object>} - نتائج الإصلاح
 */
export const fixCashboxProfitsFromTransactions = async () => {
  try {
    console.log('🔧 بدء إصلاح الأرباح في الخزينة من المعاملات الفعلية...');

    // الحصول على إجمالي الأرباح من معاملات البيع
    const salesProfitResult = await window.api.executeSQL(`
      SELECT SUM(profit) as total_sales_profit
      FROM transactions
      WHERE transaction_type = 'sale'
        AND profit IS NOT NULL
    `);

    const totalSalesProfit = salesProfitResult[0]?.total_sales_profit || 0;
    console.log(`💰 إجمالي أرباح المبيعات: ${totalSalesProfit}`);

    // الحصول على إجمالي الأرباح المفقودة من معاملات الإرجاع
    const returnsProfitResult = await window.api.executeSQL(`
      SELECT SUM(ABS(profit)) as total_returns_profit
      FROM transactions
      WHERE transaction_type = 'return'
        AND profit IS NOT NULL
    `);

    const totalReturnsProfit = returnsProfitResult[0]?.total_returns_profit || 0;
    console.log(`📉 إجمالي أرباح الإرجاعات: ${totalReturnsProfit}`);

    // حساب صافي الأرباح
    const netProfit = totalSalesProfit - totalReturnsProfit;
    console.log(`📊 صافي الأرباح: ${netProfit}`);

    // الحصول على الخزينة الحالية
    const cashboxResult = await window.api.executeSQL('SELECT * FROM cashbox LIMIT 1');
    const currentCashbox = cashboxResult[0];

    if (!currentCashbox) {
      throw new Error('لم يتم العثور على خزينة في قاعدة البيانات');
    }

    const oldProfit = currentCashbox.profit_total || 0;
    console.log(`🏦 الأرباح الحالية في الخزينة: ${oldProfit}`);
    console.log(`🔄 الأرباح الجديدة المحسوبة: ${netProfit}`);
    console.log(`📈 الفرق: ${netProfit - oldProfit}`);

    // تحديث الخزينة بالأرباح الصحيحة
    await window.api.executeSQL(`
      UPDATE cashbox
      SET profit_total = ?,
          updated_at = ?
      WHERE id = ?
    `, [netProfit, new Date().toISOString(), currentCashbox.id]);

    console.log('✅ تم تحديث الأرباح في الخزينة بنجاح');

    return {
      success: true,
      oldProfit,
      newProfit: netProfit,
      difference: netProfit - oldProfit,
      salesProfit: totalSalesProfit,
      returnsProfit: totalReturnsProfit
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح الأرباح:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * إصلاح جميع الأرباح في قاعدة البيانات
 * @returns {Promise<Object>} - نتائج الإصلاح
 */
export const fixAllProfitsInDatabase = async () => {
  try {
    console.log('🔧 بدء إصلاح الأرباح في قاعدة البيانات...');

    // الحصول على جميع معاملات البيع
    const salesTransactions = await window.api.executeSQL(`
      SELECT
        t.id,
        t.item_id,
        t.quantity,
        t.selling_price,
        t.profit as old_profit,
        t.transport_cost,
        i.avg_price
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
      ORDER BY t.id
    `);

    console.log(`📊 تم العثور على ${salesTransactions.length} معاملة بيع`);

    let updatedCount = 0;
    let totalOldProfit = 0;
    let totalNewProfit = 0;

    for (const transaction of salesTransactions) {
      const {
        id,
        item_id,
        quantity,
        selling_price,
        old_profit,
        transport_cost,
        avg_price
      } = transaction;

      // حساب مصاريف النقل المخصصة لهذا الصنف
      let transportCostPerUnit = 0;

      try {
        const purchases = await window.api.executeSQL(`
          SELECT quantity, transport_cost
          FROM transactions
          WHERE item_id = ?
            AND transaction_type = 'purchase'
            AND transport_cost > 0
        `, [item_id]);

        if (purchases && purchases.length > 0) {
          let totalPurchasedQuantity = 0;
          let totalTransportCost = 0;

          purchases.forEach(purchase => {
            totalPurchasedQuantity += purchase.quantity || 0;
            totalTransportCost += purchase.transport_cost || 0;
          });

          if (totalPurchasedQuantity > 0) {
            transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
          }
        }
      } catch (error) {
        console.warn(`⚠️ خطأ في حساب مصاريف النقل للصنف ${item_id}:`, error.message);
        transportCostPerUnit = 0;
      }

      // حساب الربح الجديد مع طرح مصاريف النقل
      let newProfit = 0;

      if (selling_price > 0 && avg_price > 0) {
        // حساب الربح الأساسي
        const basicProfit = (selling_price - avg_price) * quantity;

        // خصم مصاريف النقل
        newProfit = Math.max(0, basicProfit - (transportCostPerUnit * quantity));
      }

      // تحديث الربح إذا كان مختلفاً
      if (Math.abs(newProfit - (old_profit || 0)) > 0.01) {
        await window.api.executeSQL(`
          UPDATE transactions
          SET profit = ?
          WHERE id = ?
        `, [newProfit, id]);

        updatedCount++;

        console.log(`🔄 معاملة ${id}: ${old_profit || 0} → ${newProfit} (فرق: ${newProfit - (old_profit || 0)})`);
        console.log(`   📦 الصنف ${item_id}: (${selling_price} - ${avg_price}) × ${quantity} - ${transportCostPerUnit * quantity} = ${newProfit}`);
      }

      totalOldProfit += old_profit || 0;
      totalNewProfit += newProfit;
    }

    console.log('\n📈 ملخص الإصلاح:');
    console.log(`✅ تم تحديث ${updatedCount} معاملة من أصل ${salesTransactions.length}`);
    console.log(`💰 إجمالي الأرباح القديمة: ${totalOldProfit.toFixed(2)}`);
    console.log(`💰 إجمالي الأرباح الجديدة: ${totalNewProfit.toFixed(2)}`);
    console.log(`📊 الفرق: ${(totalNewProfit - totalOldProfit).toFixed(2)}`);

    // تحديث إجمالي الأرباح في الخزينة
    console.log('\n🏦 تحديث الخزينة...');

    const result = await window.api.executeSQL(`
      UPDATE cashbox
      SET profit_total = ?,
          updated_at = ?
      WHERE id = 1
    `, [totalNewProfit, new Date().toISOString()]);

    if (result && result.changes > 0) {
      console.log(`✅ تم تحديث إجمالي الأرباح في الخزينة إلى: ${totalNewProfit.toFixed(2)}`);
    } else {
      console.log('⚠️ لم يتم العثور على خزينة لتحديثها');
    }

    console.log('\n🎉 تم إكمال إصلاح الأرباح بنجاح!');
    console.log('💡 يرجى تحديث الصفحة لرؤية التغييرات');

    return {
      success: true,
      updatedCount,
      totalOldProfit,
      totalNewProfit,
      difference: totalNewProfit - totalOldProfit
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح الأرباح:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * دالة سريعة لتشغيل الإصلاح من وحدة التحكم
 */
window.fixProfits = fixAllProfitsInDatabase;
