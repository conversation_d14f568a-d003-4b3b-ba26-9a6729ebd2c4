/**
 * حل بديل قوي لإجبار عرض الأرباح الصحيحة
 * يتجاوز جميع المشاكل المحتملة ويضمن عرض القيم الصحيحة
 */

/**
 * إجبار تحديث الأرباح في جميع مكونات التقارير
 * @returns {Promise<Object>} - نتائج الإصلاح
 */
export const forceCorrectProfitDisplay = async () => {
  try {
    console.log('🔧 بدء الإصلاح القوي للأرباح...');

    // 1. الحصول على الأرباح الصحيحة من الخزينة
    const cashboxResult = await window.api.executeSQL('SELECT profit_total FROM cashbox LIMIT 1');
    const correctProfit = cashboxResult[0]?.profit_total || 0;
    
    console.log(`💰 الأرباح الصحيحة من الخزينة: ${correctProfit}`);

    // 2. حساب الأرباح يدوياً من المعاملات كتأكيد
    const salesResult = await window.api.executeSQL(`
      SELECT 
        t.id, t.item_id, t.quantity, t.selling_price, t.profit,
        i.avg_price
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
    `);

    let manualProfit = 0;
    for (const transaction of salesResult) {
      if (transaction.profit !== null && transaction.profit !== undefined) {
        manualProfit += transaction.profit;
      } else if (transaction.selling_price > 0 && transaction.avg_price > 0) {
        // حساب الربح الأساسي
        const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;
        
        // حساب مصاريف النقل المخصصة
        const transportResult = await window.api.executeSQL(`
          SELECT 
            SUM(transport_cost) as total_transport,
            SUM(quantity) as total_quantity
          FROM transactions
          WHERE item_id = ? AND transaction_type = 'purchase' AND transport_cost > 0
        `, [transaction.item_id]);

        let transportCostPerUnit = 0;
        if (transportResult[0] && transportResult[0].total_quantity > 0) {
          transportCostPerUnit = transportResult[0].total_transport / transportResult[0].total_quantity;
        }

        const finalProfit = basicProfit - (transportCostPerUnit * transaction.quantity);
        manualProfit += finalProfit;
      }
    }

    console.log(`🧮 الأرباح المحسوبة يدوياً: ${manualProfit}`);

    // 3. استخدام القيمة الأكثر دقة
    const finalCorrectProfit = Math.abs(correctProfit - manualProfit) < 0.01 ? correctProfit : manualProfit;
    
    console.log(`✅ الأرباح النهائية الصحيحة: ${finalCorrectProfit}`);

    // 4. إجبار تحديث جميع مكونات واجهة المستخدم
    const updateData = {
      correctProfit: finalCorrectProfit,
      timestamp: new Date().toISOString(),
      source: 'forceProfitFix'
    };

    // إرسال أحداث متعددة لضمان التحديث
    const events = [
      'force-profit-update',
      'profits-corrected',
      'financial-data-override',
      'profit-display-fix'
    ];

    events.forEach(eventName => {
      const event = new CustomEvent(eventName, { detail: updateData });
      window.dispatchEvent(event);
    });

    // 5. تحديث localStorage كنسخة احتياطية
    localStorage.setItem('correctProfitValue', finalCorrectProfit.toString());
    localStorage.setItem('profitLastUpdated', new Date().toISOString());

    // 6. إجبار إعادة رسم المكونات
    if (window.forceComponentRerender) {
      window.forceComponentRerender();
    }

    console.log('🎉 تم إكمال الإصلاح القوي للأرباح');

    return {
      success: true,
      correctProfit: finalCorrectProfit,
      cashboxProfit: correctProfit,
      manualProfit: manualProfit,
      difference: Math.abs(correctProfit - manualProfit)
    };

  } catch (error) {
    console.error('❌ خطأ في الإصلاح القوي للأرباح:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * إنشاء مكون عرض أرباح مخصص يتجاوز المشاكل
 * @param {number} fallbackValue - القيمة الاحتياطية
 * @returns {number} - القيمة الصحيحة للأرباح
 */
export const getCorrectProfitValue = async (fallbackValue = 0) => {
  try {
    // 1. محاولة الحصول على القيمة من localStorage
    const storedValue = localStorage.getItem('correctProfitValue');
    if (storedValue && !isNaN(parseFloat(storedValue))) {
      const lastUpdated = localStorage.getItem('profitLastUpdated');
      const timeDiff = new Date() - new Date(lastUpdated);
      
      // إذا كانت القيمة محدثة خلال آخر 5 دقائق، استخدمها
      if (timeDiff < 5 * 60 * 1000) {
        console.log(`💾 استخدام القيمة المحفوظة: ${storedValue}`);
        return parseFloat(storedValue);
      }
    }

    // 2. الحصول على القيمة من الخزينة
    if (window.api && window.api.executeSQL) {
      const result = await window.api.executeSQL('SELECT profit_total FROM cashbox LIMIT 1');
      if (result && result[0] && result[0].profit_total !== null) {
        const profit = result[0].profit_total;
        
        // حفظ القيمة في localStorage
        localStorage.setItem('correctProfitValue', profit.toString());
        localStorage.setItem('profitLastUpdated', new Date().toISOString());
        
        console.log(`🏦 استخدام قيمة الخزينة: ${profit}`);
        return profit;
      }
    }

    // 3. استخدام القيمة الاحتياطية
    console.log(`🔄 استخدام القيمة الاحتياطية: ${fallbackValue}`);
    return fallbackValue;

  } catch (error) {
    console.error('خطأ في الحصول على القيمة الصحيحة للأرباح:', error);
    return fallbackValue;
  }
};

/**
 * مراقب للتأكد من عرض القيم الصحيحة
 */
export const startProfitDisplayMonitor = () => {
  console.log('🔍 بدء مراقبة عرض الأرباح...');

  // مراقبة كل 10 ثوان
  const monitorInterval = setInterval(async () => {
    try {
      const correctValue = await getCorrectProfitValue();
      
      // البحث عن عناصر عرض الأرباح في الصفحة
      const profitElements = document.querySelectorAll('[data-profit-display], .profit-card .stat-card-value, .profit-value');
      
      profitElements.forEach(element => {
        const currentText = element.textContent || element.innerText;
        const currentValue = parseFloat(currentText.replace(/[^\d.-]/g, ''));
        
        // إذا كانت القيمة المعروضة مختلفة عن القيمة الصحيحة
        if (!isNaN(currentValue) && Math.abs(currentValue - correctValue) > 0.01) {
          console.log(`🔧 تصحيح قيمة معروضة: ${currentValue} → ${correctValue}`);
          
          // تحديث النص
          const formattedValue = new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
          }).format(correctValue);
          
          element.textContent = formattedValue;
          element.style.color = '#28a745'; // لون أخضر للإشارة إلى التصحيح
        }
      });

    } catch (error) {
      console.error('خطأ في مراقبة عرض الأرباح:', error);
    }
  }, 10000);

  // إيقاف المراقبة بعد 5 دقائق
  setTimeout(() => {
    clearInterval(monitorInterval);
    console.log('⏹️ تم إيقاف مراقبة عرض الأرباح');
  }, 5 * 60 * 1000);

  return monitorInterval;
};

// تصدير الدوال للاستخدام العام
if (typeof window !== 'undefined') {
  window.forceCorrectProfitDisplay = forceCorrectProfitDisplay;
  window.getCorrectProfitValue = getCorrectProfitValue;
  window.startProfitDisplayMonitor = startProfitDisplayMonitor;
  
  console.log('🛠️ تم تحميل أدوات الإصلاح القوي:');
  console.log('- window.forceCorrectProfitDisplay() - إصلاح قوي شامل');
  console.log('- window.getCorrectProfitValue() - الحصول على القيمة الصحيحة');
  console.log('- window.startProfitDisplayMonitor() - مراقبة العرض');
}
