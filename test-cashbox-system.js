/**
 * اختبار شامل لنظام الخزينة
 * يتحقق من جميع وظائف الخزينة وتسجيل المشتريات
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const path = require('path');
const { app } = require('electron');

/**
 * اختبار شامل لنظام الخزينة
 */
async function testCashboxSystem() {
  console.log('🧪 بدء الاختبار الشامل لنظام الخزينة...');

  try {
    // 1. تهيئة قاعدة البيانات
    console.log('\n1️⃣ تهيئة قاعدة البيانات...');
    const dbManager = DatabaseManager.getInstance();

    // تحديد مسار قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'wms-database.db');
    console.log('مسار قاعدة البيانات:', dbPath);

    // تهيئة قاعدة البيانات
    await dbManager.initialize(dbPath);

    // الحصول على اتصال قاعدة البيانات
    const db = dbManager.getConnection();
    if (!db) {
      throw new Error('فشل في الاتصال بقاعدة البيانات');
    }
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 2. اختبار هيكل جدول الخزينة
    console.log('\n2️⃣ اختبار هيكل جدول الخزينة...');
    const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    const existingColumns = tableInfo.map(col => col.name);

    const requiredColumns = [
      'id', 'initial_balance', 'current_balance', 'profit_total',
      'sales_total', 'purchases_total', 'returns_total', 'transport_total'
    ];

    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));

    if (missingColumns.length > 0) {
      console.log('❌ أعمدة مفقودة في جدول الخزينة:', missingColumns);
      return { success: false, message: `أعمدة مفقودة: ${missingColumns.join(', ')}` };
    }

    console.log('✅ جميع الأعمدة المطلوبة موجودة');
    console.log('📋 الأعمدة الموجودة:', existingColumns);

    // 3. اختبار قراءة بيانات الخزينة
    console.log('\n3️⃣ اختبار قراءة بيانات الخزينة...');
    let cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

    if (!cashbox) {
      console.log('⚠️ لا توجد بيانات خزينة، جاري إنشاء خزينة تجريبية...');

      // إنشاء خزينة تجريبية
      const createCashboxStmt = db.prepare(`
        INSERT INTO cashbox (
          initial_balance, current_balance, profit_total, sales_total, purchases_total,
          returns_total, transport_total, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const now = new Date().toISOString();
      const result = createCashboxStmt.run(
        1000, // الرصيد الافتتاحي
        1000, // الرصيد الحالي
        0,    // إجمالي الأرباح
        0,    // إجمالي المبيعات
        0,    // إجمالي المشتريات
        0,    // إجمالي المرتجعات
        0,    // إجمالي مصاريف النقل
        now,  // تاريخ الإنشاء
        now   // تاريخ التحديث
      );

      console.log('✅ تم إنشاء خزينة تجريبية بنجاح');

      // قراءة الخزينة المنشأة
      cashbox = db.prepare('SELECT * FROM cashbox WHERE id = ?').get(result.lastInsertRowid);
    }

    console.log('✅ تم قراءة بيانات الخزينة بنجاح');
    console.log('📊 بيانات الخزينة:');
    console.log(`   - ID: ${cashbox.id}`);
    console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${cashbox.sales_total || 0}`);
    console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total || 0}`);
    console.log(`   - إجمالي المرتجعات: ${cashbox.returns_total || 0}`);
    console.log(`   - إجمالي مصاريف النقل: ${cashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${cashbox.profit_total || 0}`);

    // 4. اختبار تحديث الخزينة (محاكاة عملية شراء)
    console.log('\n4️⃣ اختبار تحديث الخزينة (محاكاة عملية شراء)...');

    const testPurchase = {
      amount: 500,
      transport_cost: 50
    };

    const originalBalance = cashbox.current_balance;
    const originalPurchases = cashbox.purchases_total || 0;
    const originalTransport = cashbox.transport_total || 0;

    // محاكاة تحديث الخزينة لعملية شراء
    const updateQuery = `
      UPDATE cashbox
      SET current_balance = current_balance - ?,
          purchases_total = purchases_total + ?,
          transport_total = transport_total + ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const totalDeduction = testPurchase.amount + testPurchase.transport_cost;
    const updateResult = db.prepare(updateQuery).run(
      totalDeduction,
      testPurchase.amount,
      testPurchase.transport_cost,
      cashbox.id
    );

    if (updateResult.changes === 0) {
      console.log('❌ فشل في تحديث الخزينة');
      return { success: false, message: 'فشل في تحديث الخزينة' };
    }

    console.log('✅ تم تحديث الخزينة بنجاح');
    console.log(`📊 تفاصيل التحديث:`);
    console.log(`   - مبلغ الشراء: ${testPurchase.amount}`);
    console.log(`   - مصاريف النقل: ${testPurchase.transport_cost}`);
    console.log(`   - إجمالي الخصم: ${totalDeduction}`);

    // 5. التحقق من التحديث
    console.log('\n5️⃣ التحقق من صحة التحديث...');
    const updatedCashbox = db.prepare('SELECT * FROM cashbox WHERE id = ?').get(cashbox.id);

    const expectedBalance = originalBalance - totalDeduction;
    const expectedPurchases = originalPurchases + testPurchase.amount;
    const expectedTransport = originalTransport + testPurchase.transport_cost;

    console.log('📊 مقارنة القيم:');
    console.log(`   الرصيد الحالي: ${updatedCashbox.current_balance} (متوقع: ${expectedBalance})`);
    console.log(`   إجمالي المشتريات: ${updatedCashbox.purchases_total} (متوقع: ${expectedPurchases})`);
    console.log(`   إجمالي مصاريف النقل: ${updatedCashbox.transport_total} (متوقع: ${expectedTransport})`);

    const balanceCorrect = Math.abs(updatedCashbox.current_balance - expectedBalance) < 0.01;
    const purchasesCorrect = Math.abs(updatedCashbox.purchases_total - expectedPurchases) < 0.01;
    const transportCorrect = Math.abs(updatedCashbox.transport_total - expectedTransport) < 0.01;

    if (!balanceCorrect || !purchasesCorrect || !transportCorrect) {
      console.log('❌ قيم التحديث غير صحيحة');
      return { success: false, message: 'قيم التحديث غير صحيحة' };
    }

    console.log('✅ جميع قيم التحديث صحيحة');

    // 6. التراجع عن التحديث التجريبي
    console.log('\n6️⃣ التراجع عن التحديث التجريبي...');

    const rollbackQuery = `
      UPDATE cashbox
      SET current_balance = current_balance + ?,
          purchases_total = purchases_total - ?,
          transport_total = transport_total - ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const rollbackResult = db.prepare(rollbackQuery).run(
      totalDeduction,
      testPurchase.amount,
      testPurchase.transport_cost,
      cashbox.id
    );

    if (rollbackResult.changes === 0) {
      console.log('❌ فشل في التراجع عن التحديث');
      return { success: false, message: 'فشل في التراجع عن التحديث' };
    }

    console.log('✅ تم التراجع عن التحديث التجريبي بنجاح');

    // 7. اختبار معاملات الخزينة
    console.log('\n7️⃣ اختبار معاملات الخزينة...');

    const transactionQuery = `
      INSERT INTO cashbox_transactions (
        type, amount, source, notes, created_at
      )
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `;

    const testTransaction = db.prepare(transactionQuery).run(
      'expense',
      testPurchase.amount + testPurchase.transport_cost,
      'purchase',
      `اختبار معاملة شراء بقيمة ${testPurchase.amount} + مصاريف نقل ${testPurchase.transport_cost}`
    );

    if (testTransaction.changes === 0) {
      console.log('❌ فشل في إضافة معاملة الخزينة');
      return { success: false, message: 'فشل في إضافة معاملة الخزينة' };
    }

    console.log('✅ تم إضافة معاملة الخزينة بنجاح');
    console.log(`📊 معرف المعاملة: ${testTransaction.lastInsertRowid}`);

    // حذف المعاملة التجريبية
    const deleteTransactionQuery = 'DELETE FROM cashbox_transactions WHERE id = ?';
    db.prepare(deleteTransactionQuery).run(testTransaction.lastInsertRowid);
    console.log('🗑️ تم حذف المعاملة التجريبية');

    // 8. اختبار استيراد مدير المعاملات
    console.log('\n8️⃣ اختبار استيراد مدير المعاملات...');

    try {
      const transactionManager = require('./unified-transaction-manager');

      if (typeof transactionManager.updateCashboxAfterTransaction === 'function') {
        console.log('✅ دالة updateCashboxAfterTransaction موجودة');
      } else {
        console.log('❌ دالة updateCashboxAfterTransaction غير موجودة');
        return { success: false, message: 'دالة updateCashboxAfterTransaction غير موجودة' };
      }

      console.log('✅ تم استيراد مدير المعاملات بنجاح');
    } catch (importError) {
      console.log('❌ فشل في استيراد مدير المعاملات:', importError.message);
      return { success: false, message: `فشل في استيراد مدير المعاملات: ${importError.message}` };
    }

    // النتيجة النهائية
    console.log('\n🎉 نجح الاختبار الشامل لنظام الخزينة!');
    console.log('✅ جميع الوظائف تعمل بشكل صحيح');

    return {
      success: true,
      message: 'نجح الاختبار الشامل لنظام الخزينة',
      details: {
        cashboxId: cashbox.id,
        columnsCount: existingColumns.length,
        hasTransportColumn: existingColumns.includes('transport_total'),
        canUpdateCashbox: true,
        canAddTransactions: true,
        managerImported: true
      }
    };

  } catch (error) {
    console.error('❌ خطأ في الاختبار الشامل:', error);
    logError(error, 'testCashboxSystem');

    return {
      success: false,
      message: `خطأ في الاختبار الشامل: ${error.message}`
    };
  }
}

/**
 * اختبار محاكاة عملية شراء كاملة
 */
async function simulatePurchaseTransaction() {
  console.log('\n🛒 محاكاة عملية شراء كاملة...');

  try {
    const transactionManager = require('./unified-transaction-manager');

    // بيانات عملية شراء تجريبية
    const testPurchase = {
      item_id: 1,
      transaction_type: 'purchase',
      quantity: 2,
      price: 100,
      selling_price: 150,
      transport_cost: 20,
      notes: 'اختبار عملية شراء',
      user_id: 1,
      transaction_date: new Date().toISOString()
    };

    console.log('📦 بيانات عملية الشراء التجريبية:');
    console.log(`   - الصنف: ${testPurchase.item_id}`);
    console.log(`   - الكمية: ${testPurchase.quantity}`);
    console.log(`   - سعر الشراء: ${testPurchase.price}`);
    console.log(`   - سعر البيع: ${testPurchase.selling_price}`);
    console.log(`   - مصاريف النقل: ${testPurchase.transport_cost}`);
    console.log(`   - إجمالي التكلفة: ${(testPurchase.quantity * testPurchase.price) + testPurchase.transport_cost}`);

    // محاكاة استدعاء دالة تحديث الخزينة
    const result = transactionManager.updateCashboxAfterTransaction(
      testPurchase.transaction_type,
      testPurchase.quantity * testPurchase.price,
      0, // الربح = 0 في عمليات الشراء
      testPurchase.user_id,
      testPurchase.transport_cost
    );

    if (result && result.success) {
      console.log('✅ نجحت محاكاة عملية الشراء');
      console.log('📊 تم تحديث الخزينة بنجاح');
    } else {
      console.log('❌ فشلت محاكاة عملية الشراء');
      console.log('📊 خطأ:', result ? result.error : 'نتيجة غير محددة');
    }

    return result;

  } catch (error) {
    console.error('❌ خطأ في محاكاة عملية الشراء:', error);
    return { success: false, error: error.message };
  }
}

module.exports = {
  testCashboxSystem,
  simulatePurchaseTransaction
};

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء الاختبار الشامل لنظام الخزينة...');

  testCashboxSystem().then(result => {
    console.log('\n📋 نتيجة الاختبار الشامل:', result);

    if (result.success) {
      console.log('\n🛒 تشغيل محاكاة عملية شراء...');
      return simulatePurchaseTransaction();
    }
  }).then(purchaseResult => {
    if (purchaseResult) {
      console.log('\n📋 نتيجة محاكاة الشراء:', purchaseResult);
    }
    console.log('\n🏁 انتهى الاختبار');
  }).catch(error => {
    console.error('❌ خطأ في تشغيل الاختبار:', error);
  });
}
